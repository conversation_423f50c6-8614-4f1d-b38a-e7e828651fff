import Vue from 'vue'
import Router from 'vue-router'
import Main from '@/layouts/index'

import { getToken } from '@/utils/auth' // get token from cookie

Vue.use(Router)

export const constantRoutes = [
  {
    path: '/',
    redirect: 'Login',
    component: () => import('@/views/Login')
  },
  {
    path: '/Login',
    name: 'Login',
    component: () => import('@/views/Login')
  },
  {
    path: '/main',
    name: 'Main',
    component: Main,
    children:[
      {
        path: '/Template',
        name: 'Template',
        component: () => import('@/views/Template')
      },
      {
        path: '/Material',
        name: 'Material',
        component: () => import('@/views/Material')
      },
      {
        path: '/Account',
        name: 'Account',
        component: () => import('@/views/Account')
      },
      {
        path: '/EquipmentCenter',
        name: 'EquipmentCenter',
        component: () => import('@/views/EquipmentCenter')
      },
      {
        path: '/Resource',
        name: 'Resource',
        component: () => import('@/views/Resource')
      },
      {
        path: '/OperationLog',
        name: 'OperationLog',
        component: () => import('@/views/OperationLog')
      }
    ]
  }
]

const createRouter = () =>
  new Router({
    mode: 'hash', // require service support
    base: '/',
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
  })

const router = createRouter()

// 导航守卫
// 使用 router.beforeEach 注册一个全局前置守卫，判断用户是否登陆
router.beforeEach((to, from, next) => {
  if (to.path === '/login') {
    next();
  } else {
    const hasToken = getToken()
    console.log(hasToken)
    if (hasToken === null || !hasToken) {
      next('/login');
    } else {
      next();
    }
  }
});

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router