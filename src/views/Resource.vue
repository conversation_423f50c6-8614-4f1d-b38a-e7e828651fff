<template>
  <div>
    <el-form
      :model="form"
      ref="form"
      label-width="120px"
      label-position="left"
      class="demo-ruleForm"
    >
      <el-row :gutter="20">
        <el-col :span="4">
          <el-form-item :label="$t('resource.form.name')" prop="name">
            <el-input
              v-model="form.name"
              :placeholder="$t('resource.form.namePlaceholder')"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item :label="$t('resource.form.packName')" prop="packName">
            <el-input
              v-model="form.packName"
              :placeholder="$t('resource.form.packNamePlaceholder')"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item :label="$t('resource.form.date')" prop="date">
            <el-date-picker
              v-model="form.date"
              value-format="timestamp"
              type="daterange"
              :range-separator="$t('public.to')"
              :start-placeholder="$t('public.startDate')"
              :end-placeholder="$t('public.endDate')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" justify="space-between">
        <el-button
          type="primary"
          @click="openResourceSelectionDialog"
          size="mini"
          >{{ $t("resource.button.sendByRule") }}</el-button
        >
        <el-col :span="4">
          <el-button @click="resetForm('form', 'getList')" size="mini">{{
            $t("public.reset")
          }}</el-button>
          <el-button type="primary" @click="searchForm()" size="mini">{{
            $t("public.search")
          }}</el-button>
        </el-col>
      </el-row>
    </el-form>

    <div
      style="
        height: 60vh;
        background-color: #fff;
        margin: 10px 0;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        padding: 16px;
      "
    >
      <el-table
        :data="dataList"
        style="width: 100%"
        border
        height="100%"
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          fontWeight: 'bold',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }"
        :row-style="{ cursor: 'pointer', transition: 'all 0.3s ease' }"
        :cell-style="{ padding: '12px 16px', borderColor: '#ebeef5' }"
      >
        <el-table-column
          prop="num"
          :label="$t('resource.table.num')"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          :label="$t('resource.table.name')"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="pack_name"
          :label="$t('resource.table.pack_name')"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="created_at"
          :label="$t('resource.table.created_at')"
          align="center"
        >
          <template slot-scope="scope">
            {{ $formatTimeStamp(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('public.operation')"
          fixed="right"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="sendAll(scope.row.id)"
              >{{ $t("resource.button.sendAll") }}</el-button
            >
            <el-button
              type="text"
              size="small"
              @click="
                isShow = true;
                sendPartId = scope.row.id;
                pack_name = scope.row.name;
                pageNum1 = 1;
                getEquipmentList();
              "
              >{{ $t("resource.button.sendPart") }}</el-button
            >
            <el-popconfirm
              :title="$t('resource.table.deleteResource')"
              style="margin-left: 10px"
              @confirm="del(scope.row.id)"
            >
              <el-button
                type="text"
                size="small"
                slot="reference"
                style="color: #ff0000"
                >{{ $t("public.delete") }}</el-button
              >
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-row :gutter="20" type="flex" justify="end">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageNum"
        :page-sizes="[10, 20, 50]"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total"
      >
      </el-pagination>
    </el-row>

    <el-dialog
      :title="$t('resource.dialog.title.selectDevice')"
      :visible.sync="isShow"
      width="80%"
      @close="close"
    >
      <el-form
        :model="equipmentForm"
        ref="equipmentForm"
        label-width="100px"
        label-position="left"
        class="demo-ruleForm"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="$t('resource.form.deviceName')" prop="name">
              <el-input
                v-model="equipmentForm.name"
                :placeholder="$t('resource.form.deviceNamePlaceholder')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :label="$t('resource.form.deviceId')"
              prop="device_id"
            >
              <el-input
                v-model="equipmentForm.device_id"
                :placeholder="$t('resource.form.deviceIdPlaceholder')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('resource.form.date')" prop="date">
              <el-date-picker
                v-model="equipmentForm.date"
                value-format="timestamp"
                type="datetimerange"
                :range-separator="$t('public.to')"
                :start-placeholder="$t('public.startDate')"
                :end-placeholder="$t('public.endDate')"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="end">
          <el-col :span="4">
            <el-button
              @click="resetForm('equipmentForm', 'getEquipmentList')"
              >{{ $t("public.reset") }}</el-button
            >
            <el-button
              type="primary"
              @click="
                pageNum1 = 1;
                getEquipmentList();
              "
              >{{ $t("public.search") }}</el-button
            >
          </el-col>
        </el-row>
      </el-form>
      <!-- 添加 row-key 和 reserve-selection -->
      <el-table
        v-loading="isLoading"
        ref="singleTable"
        tooltip-effect="dark"
        @selection-change="handleSelectionChange"
        :data="equipmentList"
        style="width: 100%"
        border
        :row-key="(row) => row.id"
      >
        <el-table-column
          type="selection"
          width="40"
          reserve-selection
          :selectable="selectable"
        >
        </el-table-column>
        <el-table-column
          prop="num"
          :label="$t('resource.table.num')"
          width="80"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          :label="$t('resource.table.name')"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="device_id"
          :label="$t('resource.form.deviceId')"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="alias_name"
          :label="$t('resource.form.deviceAliasName')"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="created_at"
          :label="$t('resource.table.created_at')"
          align="center"
        >
          <template slot-scope="scope">
            {{ $formatTimeStamp(scope.row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
      <el-row :gutter="20" type="flex" justify="end">
        <el-pagination
          background
          @size-change="handleEquipmentSizeChange"
          @current-change="handleEquipmentCurrentChange"
          :current-page.sync="pageNum1"
          :page-sizes="[10, 20, 50]"
          :page-size="pageSize1"
          layout="total, prev, pager, next"
          :total="total1"
        >
        </el-pagination>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShow = false">{{ $t("public.cancel") }}</el-button>
        <el-button type="primary" @click="confirm()">{{
          $t("public.confirm")
        }}</el-button>
      </span>
    </el-dialog>

    <!-- 新增的资源选择弹窗 -->
    <el-dialog
      :title="$t('resource.dialog.title.selectResource')"
      :visible.sync="isResourceSelectionDialogVisible"
      width="60%"
    >
      <el-form
        :model="resourceForm"
        ref="resourceForm"
        label-width="80px"
        label-position="left"
        class="demo-ruleForm"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="$t('resource.form.name')" prop="name">
              <el-input
                v-model="resourceForm.name"
                :placeholder="$t('resource.form.namePlaceholder')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('resource.form.packName')" prop="packName">
              <el-input
                v-model="resourceForm.packName"
                :placeholder="$t('resource.form.packNamePlaceholder')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item :label="$t('resource.form.date')" prop="date">
              <el-date-picker
                v-model="resourceForm.date"
                value-format="timestamp"
                type="datetimerange"
                :range-separator="$t('public.to')"
                :start-placeholder="$t('public.startDate')"
                :end-placeholder="$t('public.endDate')"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="end">
          <el-col :span="4">
            <el-button @click="resetForm('resourceForm', 'getResourceList')">
              {{ $t("public.reset") }}</el-button
            >
            <el-button
              type="primary"
              @click="
                resourcePageNum = 1;
                getResourceList();
              "
              >{{ $t("public.search") }}</el-button
            >
          </el-col>
        </el-row>
      </el-form>
      <el-table
        v-loading="isResourceLoading"
        ref="resourceTable"
        tooltip-effect="dark"
        @selection-change="handleResourceSelectionChange"
        :data="resourceList"
        style="width: 100%"
        border
        :row-key="(row) => row.id"
      >
        <el-table-column
          type="selection"
          width="40"
          reserve-selection
          :selectable="selectable"
        >
        </el-table-column>
        <el-table-column prop="num" label="序号" width="80" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          :label="$t('resource.table.name')"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="pack_name"
          :label="$t('resource.table.pack_name')"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="created_at"
          :label="$t('resource.table.created_at')"
          align="center"
        >
          <template slot-scope="scope">
            {{ $formatTimeStamp(scope.row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
      <el-row :gutter="20" type="flex" justify="end">
        <el-pagination
          background
          @size-change="handleResourceSizeChange"
          @current-change="handleResourceCurrentChange"
          :current-page.sync="resourcePageNum"
          :page-sizes="[10, 20, 50]"
          :page-size="resourcePageSize"
          layout="total, prev, pager, next"
          :total="resourceTotal"
        >
        </el-pagination>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancelInResourceDialog">{{
          $t("public.cancel")
        }}</el-button>
        <el-button type="primary" @click="handleResourceSelectionConfirm"
          >{{$t('resource.button.nextStep')}}</el-button
        >
      </span>
    </el-dialog>

    <el-dialog
      :title="$t('resource.dialog.title.group_name')"
      :visible.sync="isGroupSelectionDialogVisible"
      width="60%"
    >
      <el-form
        :model="groupForm"
        ref="groupForm"
        label-width="80px"
        label-position="left"
        class="demo-ruleForm"
      >
        <!-- 这里如果后续有搜索需求可添加输入框等，目前没有搜索功能则先空着 -->
      </el-form>
      <el-table
        v-loading="isGroupLoading"
        ref="groupTable"
        tooltip-effect="dark"
        @selection-change="handleGroupSelectionChange"
        :data="groupList"
        style="width: 100%"
        border
        :row-key="(row) => row.id"
      >
        <el-table-column
          type="selection"
          width="40"
          reserve-selection
          :selectable="selectable"
        >
        </el-table-column>
        <el-table-column
          prop="num"
          :label="$t('resource.table.num')"
          width="80"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          :label="$t('resource.dialog.title.group_name')"
          align="center"
        ></el-table-column>
      </el-table>
      <el-row :gutter="20" type="flex" justify="end">
        <el-pagination
          background
          @size-change="handleGroupSizeChange"
          @current-change="handleGroupCurrentChange"
          :current-page.sync="groupPageNum"
          :page-sizes="[10, 20, 50]"
          :page-size="groupPageSize"
          layout="total, prev, pager, next"
          :total="groupTotal"
        >
        </el-pagination>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancelInGroupDialog">取 消</el-button>
        <el-button type="primary" @click="handleGroupNextStep"
          >{{$t('resource.button.nextStep')}}</el-button
        >
      </span>
    </el-dialog>

    <!-- 新增的展示选中资源的弹窗 -->
    <el-dialog
      :title="$t('resource.dialog.title.inputDevice')"
      :visible.sync="isResourceDisplayDialogVisible"
      width="60%"
    >
      <div v-if="selectedResources.length > 0">
        <el-form label-width="120px">
          <el-form-item
            v-for="resource in selectedResources"
            :key="resource.id"
            :label="resource.name"
          >
            <el-input
              v-model="resource.inputValue"
              :placeholder="$t('resource.dialog.tip.deviceAliasHint')"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div v-else>{{ $t("resource.dialog.tip.noSelectedResources") }}</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isResourceDisplayDialogVisible = false">{{
          $t("public.cancel")
        }}</el-button>
        <el-button
          @click="sendRule"
          type="primary"
          class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:ring-2 focus:ring-blue-300"
        >
          {{ $t("public.confirm") }}
        </el-button>
      </span>
    </el-dialog>

    <!-- 选择设备弹窗，这里的代码和原来点击部分发送时弹出的选择设备弹窗代码一样，可提取成一个组件更好 -->
    <el-dialog
      :title="$t('resource.dialog.title.selectDevice')"
      :visible.sync="isShowForNextStep"
      width="80%"
      @close="closeForNextStep"
    >
      <el-form
        :model="equipmentForm"
        ref="equipmentForm"
        label-width="80px"
        label-position="left"
        class="demo-ruleForm"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="$t('resource.form.deviceName')" prop="name">
              <el-input
                v-model="equipmentForm.name"
                :placeholder="$t('resource.form.deviceNamePlaceholder')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :label="$t('resource.form.deviceId')"
              prop="device_id"
            >
              <el-input
                v-model="equipmentForm.device_id"
                :placeholder="$t('resource.form.deviceIdPlaceholder')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('resource.form.date')" prop="date">
              <el-date-picker
                v-model="equipmentForm.date"
                value-format="timestamp"
                type="datetimerange"
                :range-separator="$t('public.to')"
                :start-placeholder="$t('public.startDate')"
                :end-placeholder="$t('public.endDate')"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="end">
          <el-col :span="4">
            <el-button @click="resetForm('equipmentForm', 'getEquipmentList')">
              $t("public.reset")</el-button
            >
            <el-button
              type="primary"
              @click="
                pageNum1 = 1;
                getEquipmentList();
              "
              >{{ $t("public.search") }}</el-button
            >
          </el-col>
        </el-row>
      </el-form>
      <el-table
        v-loading="isLoading"
        ref="singleTable"
        tooltip-effect="dark"
        @selection-change="handleSelectionChange"
        :data="equipmentList"
        style="width: 100%"
        border
        :row-key="(row) => row.id"
        :row-class-name="tableRowClassName"
        :cell-class-name="tableCellClassName"
      >
        <el-table-column
          type="selection"
          width="40"
          reserve-selection
          :selectable="selectable"
        ></el-table-column>
        <el-table-column
          prop="num"
          :label="$t('resource.table.num')"
          width="80"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          :label="$t('resource.form.deviceName')"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="device_id"
          :label="$t('resource.form.deviceId')"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="created_at"
          :label="$t('resource.table.created_at')"
          align="center"
        >
          <template slot-scope="scope">
            {{ $formatTimeStamp(scope.row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
      <el-row :gutter="20" type="flex" justify="end">
        <el-pagination
          background
          @size-change="handleEquipmentSizeChange"
          @current-change="handleEquipmentCurrentChange"
          :current-page.sync="pageNum1"
          :page-sizes="[10, 20, 50]"
          :page-size="pageSize1"
          layout="total, prev, pager, next"
          :total="total1"
        ></el-pagination>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowForNextStep = false">{{
          $t("public.cancel")
        }}</el-button>
        <el-button type="primary" @click="confirmForNextStep()">{{
          $t("public.confirm")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getList,
  getEquipmentList,
  del,
  send,
  getGroupList,
} from "@/api/resource.js";
export default {
  data() {
    return {
      dataList: [],
      equipmentList: [],
      resourceList: [],
      groupList: [],
      pageNum: 1,
      pageSize: 10,

      // 新增分组相关数据
      groupList: [],
      groupPageNum: 1,
      groupPageSize: 10,
      groupTotal: 0,
      isGroupLoading: false,
      selectedGroupIds: [],
      isGroupSelectionDialogVisible: false,

      //部分发送存储Id
      sendPartId: 0,

      total: 0,
      pageNum1: 1,
      pageSize1: 5,
      total1: 0,
      resourcePageNum: 1,
      resourcePageSize: 10,
      resourceTotal: 0,
      form: {
        name: "",
        packName: "",
        date: [],
      },
      equipmentForm: {
        name: "",
        device_id: "",
        date: [],
      },
      // 确保 resourceForm 被正确初始化
      resourceForm: {
        name: "",
        packName: "",
        date: [],
      },

      groupForm: {
        name: "",
        packName: "",
        date: [],
      },
      id: "",
      equipment_name: "",
      equipment_id_str: "",
      pack_name: "",
      isShow: false,
      isEdit: false,
      isLoading: false,
      isResourceLoading: false,
      selectedEquipmentIds: [],
      isResourceSelectionDialogVisible: false,
      isShowForNextStep: false, // 控制点击下一步后弹出的选择设备弹窗的显示隐藏
      selectedResourceIds: [],
      // 新增用于存储选中资源信息的数据
      selectedResources: [],
      // 新增控制资源展示弹窗的变量
      isResourceDisplayDialogVisible: false,
    };
  },
  // 其他代码...

  created() {
    this.getList();
  },
  methods: {
    selectable(row, index) {
      return row.status !== "disabled";
    },
    tableRowClassName({ row, rowIndex }) {
      if (this.selectedEquipmentIds.includes(row.id)) {
        return "selected-row";
      }
      return "";
    },
    tableCellClassName({ row, column, rowIndex, columnIndex }) {
      if (
        column.property === "name" &&
        this.selectedEquipmentIds.includes(row.id)
      ) {
        return "selected-cell";
      }
      return "";
    },
    getList() {
      this.isLoading = true;
      getList({
        page: this.pageNum,
        pageSize: this.pageSize,
        name: this.form.name,
        packName: this.form.packName,
        created_at_start:
          this.form.date.length > 0 ? this.form.date[0] / 1000 : "",
        created_at_end:
          this.form.date.length > 0 ? this.form.date[1] / 1000 : "",
      }).then((res) => {
        if (res.code == 0) {
          this.dataList = res.data.data;
          this.total = res.data.total;
          this.isLoading = false;
        }
      });
    },
    getGroupList() {
      this.isGroupLoading = true;
      getGroupList({
        page: this.groupPageNum,
        pageSize: this.groupPageSize,
      }).then((res) => {
        if (res.code === 0) {
          this.groupList = res.data.data;
          this.groupTotal = res.data.total;
          this.isGroupLoading = false;
        }
      });
    },
    // 新增处理分组选择变化的方法
    handleGroupSelectionChange(val) {
      this.selectedGroupIds = [];
      val.forEach((item) => {
        this.selectedGroupIds.push(item.id);
      });
    },
    // 新增处理取消分组选择对话框的方法
    handleCancelInGroupDialog() {
      this.isGroupSelectionDialogVisible = false;
      this.selectedGroupIds = [];
      if (this.$refs.groupTable) {
        this.$refs.groupTable.clearSelection();
      }
    },
    // 新增处理确认分组选择的方法
    handleGroupSelectionConfirm() {
      this.isGroupSelectionDialogVisible = false;
      this.$message({
        type: "info",
        message: this.$t("resource.dialog.message.selectedGroups", {
          count: this.selectedGroupIds.length,
        }),
      });
      // 这里可以添加后续处理逻辑，比如继续下一步操作等
    },
    // 新增处理分组分页大小变化的方法
    handleGroupSizeChange(val) {
      this.groupPageNum = 1;
      this.groupPageSize = val;
      this.getGroupList();
    },
    // 新增处理分组当前页码变化的方法
    handleGroupCurrentChange(val) {
      this.groupPageNum = val;
      this.getGroupList();
    },
    getResourceList() {
      this.isResourceLoading = true;
      getList({
        page: this.resourcePageNum,
        pageSize: this.resourcePageSize,
        name: this.resourceForm.name,
        packName: this.resourceForm.packName,
        created_at_start:
          this.resourceForm.date.length > 0
            ? this.resourceForm.date[0] / 1000
            : "",
        created_at_end:
          this.resourceForm.date.length > 0
            ? this.resourceForm.date[1] / 1000
            : "",
      }).then((res) => {
        if (res.code === 0) {
          this.resourceList = res.data.data;
          this.resourceTotal = res.data.total;
          this.isResourceLoading = false;
        }
      });
    },
    closeForNextStep() {
      // 这里可以添加关闭弹窗时的逻辑，例如清空选择等
      this.pack_name = "";
      this.equipment_name = "";
      this.selectedEquipmentIds = [];
      this.$refs.equipmentForm.resetFields();
    },
    //部分发送
    confirmForNextStep() {
      // 这里处理选择设备后的确认逻辑，和原来的confirm方法类似
      if (!this.equipment_id_str) {
        return this.$message({
          type: "warning",
          message: this.$t("resource.dialog.tip.selectAtLeastOneDevice"),
        });
      } else {
        this.$confirm(
          this.$t("resource.confirm.sendToDevices"),
          this.$t("resource.confirm.title"),
          {
            confirmButtonText: this.$t("public.confirm"),
            cancelButtonText: this.$t("public.cancel"),
            type: "warning",
          }
        ).then(() => {
          // 这里假设发送时需要知道选中的资源id等信息，可以根据实际情况调整
          // const selectedResource = this.resourceList.find(item => this.selectedResourceIds.includes(item.id));
          // const pack_name = selectedResource ? selectedResource.pack_name : '';
          this.sendPart(this.equipment_id_str);
          this.equipment_id_str = "";
          this.sendPartId = 0;
        });
      }
    },

    handleResourceSizeChange(val) {
      this.resourcePageNum = 1;
      this.resourcePageSize = val;
      this.getResourceList();
    },
    handleResourceCurrentChange(val) {
      this.resourcePageNum = val;
      this.getResourceList();
    },

    getEquipmentList() {
      getEquipmentList({
        page: this.pageNum1,
        pageSize: this.pageSize1,
        name: this.equipmentForm.name,
        device_id: this.equipmentForm.device_id,
        created_at_start:
          this.equipmentForm.date.length > 0
            ? this.equipmentForm.date[0] / 1000
            : "",
        created_at_end:
          this.equipmentForm.date.length > 0
            ? this.equipmentForm.date[1] / 1000
            : "",
      }).then((res) => {
        if (res.code == 0) {
          this.equipmentList = res.data.data;
          this.total1 = res.data.total;
          this.equipmentList.forEach((item) => {
            if (this.selectedEquipmentIds.includes(item.id)) {
              this.$refs.singleTable.toggleRowSelection(item, true);
            }
          });
        }
      });
    },
    confirm(pack_name) {
      if (!this.equipment_id_str) {
        return this.$message({
          type: "warning",
          message: this.$t("resource.dialog.tip.selectAtLeastOneDevice"),
        });
      } else {
        this.$confirm(
          this.$t("resource.confirm.sendToDevices"),
          this.$t("resource.confirm.title"),
          {
            confirmButtonText: this.$t("public.confirm"),
            cancelButtonText: this.$t("public.cancel"),
            type: "warning",
          }
        ).then(() => {
          this.sendPart(this.equipment_id_str);
          this.equipment_id_str = "";
          this.sendPartId = 0;
          this.isShow = false; // 添加这一行来关闭弹窗
        });
      }
    },
    handleSelectionChange(val) {
      let arr = [];
      this.selectedEquipmentIds = [];
      val.forEach((item) => {
        arr.push(item.id);
        this.selectedEquipmentIds.push(item.id);
      });

      this.equipment_id_str = arr.join(",");
    },
    //全部发送
    sendAll(id) {
      const data = {
        type: 2,
        list: [
          {
            resource_id: id,
          },
        ],
      };
      send(data).then((res) => {
        if (res.code === 0) {
          this.$message({
            type: "success",
            message: this.$t("resource.dialog.message.sendSuccess"),
          });
          this.getList();
        }
      });
    },

    //按照规则发送
    sendRule() {
      // 构建请求数据
      const groupIdStr = this.selectedGroupIds.join(",");
      const list = this.selectedResources.map((resource) => ({
        resource_id: resource.id,
        equipment_name: resource.inputValue,
      }));

      const data = {
        type: 3,
        group_id: groupIdStr,
        list: list,
      };

      // 发送请求
      send(data)
        .then((res) => {
          if (res.code === 0) {
            this.$message({
              type: "success",
              message: this.$t("resource.dialog.message.sendByRuleSuccess"),
            });
            // 清空选中的数据
            this.selectedResourceIds = [];
            this.selectedResources = [];
            this.selectedGroupIds = [];
            // 清空表格的选中状态
            if (this.$refs.resourceTable) {
              this.$refs.resourceTable.clearSelection();
            }
            if (this.$refs.groupTable) {
              this.$refs.groupTable.clearSelection();
            }
            this.isResourceDisplayDialogVisible = false; // 关闭弹窗
          } else {
            this.$message({
              type: "error",
              message: this.$t("resource.dialog.message.sendFailed"),
            });
          }
        })
        .catch((error) => {
          this.$message({
            type: "error",
            message: this.$t("resource.dialog.message.requestError"),
          });
          console.error(error);
        });
    },

    //部分发送
    sendPart(equipment_id) {
      const data = {
        type: 1,
        list: [
          {
            resource_id: this.sendPartId,
            equipment_id: equipment_id,
          },
        ],
      };
      send(data).then((res) => {
        if (res.code === 0) {
          this.$message({
            type: "success",
            message: this.$t("resource.dialog.message.sendSuccess"),
          });
          this.getList();
        }
      });
    },
    send(type, pack_name, equipment_name) {
      send({
        equipment_name,
        pack_name,
        type,
      }).then((res) => {
        if (res.code == 0) {
          this.$message({
            type: "success",
            message: this.$t("resource.dialog.message.sendSuccess"),
          });
          this.getList();
        }
      });
    },
    del(id) {
      del(id).then((res) => {
        this.$message({
          type: "success",
          message: this.$t("public.deleteSuccess"),
        });
        this.getList();
      });
    },
    close() {
      this.pack_name = "";
      this.equipment_name = "";
      this.selectedEquipmentIds = [];
      this.$refs.equipmentForm.resetFields();
      // 清除表格选中状态
      if (this.$refs.singleTable) {
        this.$refs.singleTable.clearSelection();
      }
    },
    searchForm() {
      this.getList();
    },
    resetForm(form, name) {
      this.pageNum = 1;
      this.$refs[form].resetFields();
      this[name]();
    },
    handleSizeChange(val) {
      this.pageNum = 1;
      this.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
    handleEquipmentSizeChange(val) {
      this.pageNum1 = 1;
      this.pageSize1 = val;
      this.getEquipmentList();
    },
    handleEquipmentCurrentChange(val) {
      this.pageNum1 = val;
      this.getEquipmentList();
    },

    handleCancelInResourceDialog() {
      this.isResourceSelectionDialogVisible = false;
      // 清空已选中的资源 ID 数组
      this.selectedResourceIds = [];
      // 清空表格的选中状态
      if (this.$refs.resourceTable) {
        this.$refs.resourceTable.clearSelection();
      }
      // 重置表单字段
      this.$refs.resourceForm.resetFields();
    },
    openResourceSelectionDialog() {
      this.isResourceSelectionDialogVisible = true;
      this.resourcePageNum = 1; // 重置页码
      this.getResourceList(); // 调用获取资源列表方法
    },
    handleResourceSelectionChange(val) {
      this.selectedResourceIds = [];
      val.forEach((item) => {
        this.selectedResourceIds.push(item.id);
        this.selectedResources = val;
      });
    },
    handleGroupNextStep() {
      // 检查是否选择了分组
      if (this.selectedGroupIds.length === 0) {
        this.$message({
          type: "warning",
          message: this.$t("resource.dialog.tip.selectAtLeastOneGroup"),
        });
        return; // 阻止继续执行
      }

      // 关闭分组选择对话框
      this.isGroupSelectionDialogVisible = false;
      // 打开展示选中资源的弹窗
      this.isResourceDisplayDialogVisible = true;
    },
    handleCancelInGroupDialog() {
      this.isGroupSelectionDialogVisible = false;
      this.selectedGroupIds = [];
      if (this.$refs.groupTable) {
        this.$refs.groupTable.clearSelection();
      }
    },

    handleResourceSelectionConfirm() {
      // 检查是否选择了资源
      if (this.selectedResourceIds.length === 0) {
        this.$message({
          type: "warning",
          message: this.$t("resource.dialog.tip.selectAtLeastOneResource"),
        });
        return; // 阻止继续执行
      }
      this.isResourceSelectionDialogVisible = false;
      // 打开分组选择对话框并获取分组列表

      this.isGroupSelectionDialogVisible = true;
      this.groupPageNum = 1;
      this.getGroupList();
    },
  },
};
</script>

<style scoped>
.el-table .selected-row {
  transition: all 0.3s ease;
  background-color: #f0f7ff;
}

.el-table .selected-cell {
  transition: all 0.3s ease;
  color: #409eff;
  font-weight: bold;
}
.el-dialog {
  transition: all 0.3s ease;
}

.el-table__row:hover {
  transition: all 0.3s ease;
  background-color: #f5f7fa;
}

.el-table__row--striped:hover {
  transition: all 0.3s ease;
  background-color: #f5f7fa;
}

.flex {
  display: flex;
}

.img {
  width: 40px;
  height: 40px;
}
</style>
