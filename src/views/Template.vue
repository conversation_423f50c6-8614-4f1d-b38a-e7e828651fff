<template>
  <div>
    <el-form
      :model="form"
      ref="form"
      label-width="80px"
      label-position="left"
      class="demo-ruleForm"
    >
      <el-row :gutter="20">
        <el-col :span="4">
          <el-form-item :label="$t('template.form.name')" prop="name">
            <el-input
              v-model="form.name"
              :placeholder="$t('template.form.namePlaceholder')"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item :label="$t('template.form.date')">
            <el-date-picker
              v-model="form.date"
              type="daterange"
              :range-separator="$t('public.to')"
              :start-placeholder="$t('public.startDate')"
              :end-placeholder="$t('public.endDate')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" justify="space-between">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="add()"
          size="mini"
          >{{ $t("template.button.create") }}</el-button
        >
        <el-col> 
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="addwork()"
          size="mini"
          >{{ $t("template.button.createWorkTemplate") }}</el-button
        >
         </el-col>









        <el-col :span="4">
          <el-button @click="resetForm()" size="mini">{{
            $t("public.reset")
          }}</el-button>
          <el-button type="primary" @click="searchForm()" size="mini">{{
            $t("public.search")
          }}</el-button>
        </el-col>
      </el-row>
    </el-form>

    <div style="height: 60vh; background-color: #ccc; margin: 10px 0">
      <el-table :data="dataList" style="width: 100%" border height="100%">
        <el-table-column
          prop="num"
          :label="$t('template.table.num')"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          :label="$t('template.table.name')"
          width="180"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="created_at"
          :label="$t('template.table.created_at')"
          align="center"
        >
          <template slot-scope="scope">
            {{ $formatTimeStamp(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('public.operation')"
          fixed="right"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="savePack(scope.row.id)"
              >{{ $t("template.dialog.title.pack") }}</el-button
            >
            <el-button type="text" size="small" @click="edit(scope.row)">{{
              $t("public.edit")
            }}</el-button>
            <el-popconfirm
              :title="$t('template.table.sureToDelete')"
              style="margin-left: 10px"
              @confirm="del(scope.row.id)"
            >
              <el-button
                type="text"
                size="small"
                slot="reference"
                style="color: #ff0000"
                >{{ $t("public.delete") }}</el-button
              >
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-row :gutter="20" type="flex" justify="end">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageNum"
        :page-sizes="[10, 20, 50]"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total"
      >
      </el-pagination>
    </el-row>
    <el-dialog
      :title="
        isEdit
          ? $t('template.dialog.title.edit')
          : $t('template.dialog.title.add')
      "
      :visible.sync="isShowTemplate"
      width="100%"
      @close="close"
    >
      <!-- 功能区域 -->
      <div class="flex">
        <el-button type="primary" @click="addMaterial(4)" plain size="mini">{{
          $t("template.button.setBackground")
        }}</el-button>
        <el-button type="primary" @click="addMaterial(1)" plain size="mini">{{
          $t("template.button.addImage")
        }}</el-button>
        <el-button type="primary" @click="addMaterial(2)" plain size="mini">{{
          $t("template.button.addVideo")
        }}</el-button>
        <el-button type="primary" @click="addMaterial(3)" plain size="mini">{{
          $t("template.button.dateTime")
        }}</el-button>
        <!-- 新增背景图设置面板 -->
        <el-select
         v-model="templatePages[currentPage].backgroundSettings.backgroundSize"
          placeholder="背景图显示方式"
          size="mini"
          style="margin-left: 10px"
        >
          <el-option :label="$t('template.background.repeat')"  value="repeat"></el-option>
          <el-option :label="$t('template.background.cover')" value="cover"></el-option>
          <el-option :label="$t('template.background.contain')" value="contain"></el-option>
          <el-option :label="$t('template.background.auto')" value="auto"></el-option>
        </el-select>

        <el-button
          v-if="templatePages[currentPage].backgroundUrl"
          type="info"
          @click="clearBackground()"
          plain
          size="mini"
          >{{ $t("template.button.clearBackground") }}</el-button
        >
        <el-button type="info" @click="clearTemplate" plain size="mini">{{
          $t("public.reset")
        }}</el-button>
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="addNewPage()"
          size="mini"
          >{{ $t("template.button.addNewPage") }}</el-button
        >
        <el-button
          @click="prevPage()"
          :disabled="currentPage === 0"
          size="mini"
          >{{ $t("template.button.prevPage") }}</el-button
        >
        <el-button
          @click="nextPage()"
          :disabled="currentPage === templatePages.length - 1"
          size="mini"
          >{{ $t("template.button.nextPage") }}</el-button
        >
        <span class="ml-large"
          >{{ $t("template.dialog.pageInfo.currentPage") }}
          {{ currentPage + 1 }} {{ $t("template.dialog.pageInfo.totalPages") }}
          {{ templatePages.length }} {{ $t("template.button.page") }}</span
        >
      </div>
      <div class="container">
        <!-- 模板区域 -->
        <div
          class="middle-pane"
          :style="{
            backgroundImage: templatePages[currentPage].backgroundUrl
              ? `url(${imageUrl + templatePages[currentPage].backgroundUrl})`
              : 'none',
            backgroundSize:
              (templatePages[currentPage].backgroundSettings || {})
                .backgroundSize === 'repeat'
                ? 'auto'
                : (templatePages[currentPage].backgroundSettings || {})
                    .backgroundSize || 'cover',
            backgroundRepeat:
              (templatePages[currentPage].backgroundSettings || {})
                .backgroundSize === 'repeat'
                ? 'repeat'
                : 'no-repeat',
            backgroundPosition:
              (templatePages[currentPage].backgroundSettings || {})
                .backgroundPosition || 'center center',
          }"
          ref="middlePane"
        >
          <draggable-resizable
            v-for="(item, index) in templatePages[currentPage].materialList"
            :key="index"
            :x="item.x_axis"
            :y="item.y_axis"
            :w="item.width"
            :h="item.height"
            :min-width="100"
            :min-height="item.template_sm_type == 2 ? 50 : 100"
            :dragging="true"
            :resizing="true"
            :parent="true"
            @dragging="(left, top) => handleDragging(left, top, index)"
            class="draggable"
            @resizing="
              (left, top, width, height) =>
                handleResizing(left, top, width, height, index)
            "
          >
            <img
              style="width: 100%; height: 100%"
              v-if="item.type == 1 && item.template_sm_type == 1"
              :src="imageUrl + item.path"
              alt="Image"
              class="media"
            />
            <video
              style="width: 100%; height: 100%"
              v-if="item.type == 2 && item.template_sm_type == 1"
              :src="imageUrl + item.path"
              controls
              class="media"
            ></video>
            <dateTime v-if="item.template_sm_type == 2"></dateTime>
            <img
              class="del"
              @click="delMaterial(index)"
              :src="require('@/assets/delete.png')"
              alt=""
            />
          </draggable-resizable>
        </div>

        <!-- 视图列表区域 -->
        <div class="right-pane">
          <el-form
            :model="sharedAddForm"
            :rules="rules"
            ref="addForm"
            label-width="200px"
            label-position="left"
            class="demo-ruleForm"
          >
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="$t('template.form.name')" prop="name">
                  <el-input
                    v-model="sharedAddForm.name"
                    :placeholder="$t('template.form.namePlaceholder')"
                    class="uniform-width"
                  ></el-input>
                </el-form-item>

                <el-form-item
                  :label="$t('template.form.resolutionRatio')"
                  prop="resolution_ratio"
                  required
                  label-width="200px"
                >
                  <el-select
                    v-model="sharedAddForm.resolution_ratio"
                    :placeholder="
                      $t('template.form.resolutionRatioPlaceholder')
                    "
                    class="uniform-width"
                  >
                    <el-option
                      v-for="item in resolutionRatioList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.name"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div
            v-for="(item, index) in templatePages[currentPage].materialList"
            :key="index"
            class="view-item"
          >
            <div>
              {{ $t("template.table.name") }}:
              {{
                item.template_sm_type == 1
                  ? item.sm_name
                  : $t("template.dialog.materialType.dateTime")
              }}
            </div>
            <div>
              {{ $t("template.table.type") }}:
              {{
                item.type == 1 && item.template_sm_type == 1
                  ? $t("template.dialog.materialType.image")
                  : item.type == 2 && item.template_sm_type == 1
                  ? $t("template.dialog.materialType.video")
                  : $t("template.dialog.materialType.dateTime")
              }}
            </div>
            <div>W: {{ item.width }}px</div>
            <div>H: {{ item.height }}px</div>
            <div>X: {{ item.x_axis }}px</div>
            <div>Y: {{ item.y_axis }}px</div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowTemplate = false">{{
          $t("public.cancel")
        }}</el-button>
        <el-button type="primary" @click="save()">{{
          $t("public.confirm")
        }}</el-button>
      </span>
      
    </el-dialog>
<!-- 选择素材图片弹窗 -->
      <el-dialog
        title="添加"
        :append-to-body="true"
        style="margin-top: 100px"
        :visible.sync="isShowMaterial"
        width="80%"
        @close="close1"
      >
        <el-table
          v-loading="isLoading"
          ref="singleTable"
          tooltip-effect="dark"
          @select="selectChange"
          :data="materialdDataList"
          style="width: 100%"
          border
        >
          <el-table-column type="selection" width="40"> </el-table-column>
          <el-table-column
            prop="name"
            :label="$t('material.table.name')"
            width="180"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="type"
            :label="$t('material.table.type')"
            align="center"
          >
            <template slot-scope="scope">
              {{
                scope.row.type == 1
                  ? $t("template.dialog.materialType.image")
                  : $t("template.dialog.materialType.video")
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="path"
            :label="$t('material.table.preview')"
            align="center"
          >
            <template slot-scope="scope">
              <el-image
                v-if="scope.row.path && scope.row.type == 1"
                :src="imageUrl + scope.row.path"
                class="img"
                :preview-src-list="[imageUrl + scope.row.path]"
                fit="cover"
              ></el-image>
              <video
                class="img"
                v-else
                :src="imageUrl + scope.row.path"
                controls
              ></video>
            </template>
          </el-table-column>
          <el-table-column
            prop="created_at"
            :label="$t('template.table.created_at')"
            align="center"
          >
            <template slot-scope="scope">
              {{ $formatTimeStamp(scope.row.created_at) }}
            </template>
          </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button @click="isShowMaterial = false">{{
            $t("public.cancel")
          }}</el-button>
          <el-button type="primary" @click="confirmMaterial()">{{
            $t("public.confirm")
          }}</el-button>
        </span>
      </el-dialog>
<!-- 新增工作模板弹窗 -->
<el-dialog
  title="新建工作模板"
  :visible.sync="isShowWorkTemplate"
   style="margin-top: 300px"
  width="30%"
  @close="handleWorkTemplateClose"
>
  <el-form
    :model="workTemplateForm"
    :rules="workTemplateRules"
    ref="workTemplateFormRef"
    label-width="100px"
    class="demo-ruleForm"
  >
    <el-form-item
      label="模板名称"
      prop="name"
      required
    >
      <el-input
        v-model="workTemplateForm.name"
        placeholder="请输入模板名称"
      ></el-input>
    </el-form-item>



  <el-form-item label="选择素材">
      <el-button
        type="primary"
        @click="selectFile"
        style="width: 50%"
      >
        选择素材
      </el-button>
    </el-form-item>
  </el-form>

  <span slot="footer" class="dialog-footer">
    <el-button @click="isShowWorkTemplate = false">
      {{ $t('public.cancel') }}
    </el-button>
    <el-button type="primary" @click="handleSaveWorkTemplate">
      {{ $t('public.confirm') }}
    </el-button>
  </span>
</el-dialog>





    <!-- 打包弹窗 -->
    <el-dialog
      :title="$t('template.dialog.title.pack')"
      style="margin-top: 100px"
      :visible.sync="isShowPack"
      width="30%"
      @close="
        packForm.resource_pack_name = '';
        packForm.name = '';
      "
    >
      <el-form
        :model="packForm"
        :rules="packRule"
        style="padding: 20px"
        ref="packForm"
        label-width="100px"
        label-position="left"
        class="demo-ruleForm"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item
              :label="$t('template.form.resourcePackName')"
              prop="resource_pack_name"
            >
              <el-input
                v-model="packForm.resource_pack_name"
                :placeholder="$t('template.form.namePlaceholder')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              :label="$t('template.form.resourcePackAlias')"
              prop="name"
            >
              <el-input
                v-model="packForm.name"
                :placeholder="$t('template.form.resourcePackAliasPlaceholder')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowPack = false">{{
          $t("public.cancel")
        }}</el-button>
        <el-button type="primary" @click="confirmPack()">{{
          $t("public.confirm")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import DraggableResizable from "vue-draggable-resizable";
import dateTime from "@/components/dateTime.vue";
import "vue-draggable-resizable/dist/VueDraggableResizable.css";
import {
  getList,
  getMaterialList,
  getDetail,
  add,
  edit,
  del,
  savePack,
} from "@/api/template.js";

export default {
  components: { DraggableResizable, dateTime },
  data() {
    return {

      

    isShowWorkTemplate: false,
    workTemplateForm: {
      name: "", // 模板名称
    },
    workTemplateRules: {
      name: [
        { required: true, message: "请输入模板名称", trigger: "blur" },
      ],
    },
      dataList: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      pageNumM: 1,
      pageSizeM: 10,
      totalM: 0,
      // 在 data 中定义一个变量来跟踪当前页面的背景设置
      currentPageBackgroundSettings: null,
      form: {
        name: "",
        date: [],
      },
      templatePages: [
        {
          backgroundUrl: "",
          materialList: [],
          backgroundList: [],
          backgroundSettings: {
            backgroundSize: "cover",
            backgroundPosition: "center center",
          },
        },
      ],
      currentPage: 0,
      sharedAddForm: {
        name: "",
        resolution_ratio: "1920x1080",
      },
      packForm: {
        resource_pack_name: "",
        name: "",
        html_content: "",
      },
      resolutionRatioList: [
        {
          id: 0,
          name: "1920x1080",
        },
        {
          id: 1,
          name: "1440x900",
        },
        {
          id: 2,
          name: "1280x1024",
        },
        {
          id: 3,
          name: "1024x768",
        },
      ],
      rules: {
        name: [
          {
            required: true,
            message: this.$i18n.t("template.form.namePlaceholder"),
            trigger: "blur",
          },
        ],
        resolution_ratio: [
          {
            required: true,
            message: this.$i18n.t("template.form.resolutionRatioPlaceholder"),
            trigger: "change",
          },
        ],
      },
      packRule: {
        name: [
          {
            required: false,
            message: this.$i18n.t("template.form.resourcePackAliasPlaceholder"),
            trigger: "blur",
          },
        ],
        resource_pack_name: [
          {
            required: true,
            message: this.$i18n.t("template.form.resourcePackNamePlaceholder"),
            trigger: "blur",
          },
        ],
      },
      isLoading: false,
      isShowTemplate: false,
      isShowMaterial: false,
      isShowPack: false,
      isEdit: false,
      type: 1,
      selectedRow: null,
      templateId: "",
      imageUrl: process.env.VUE_APP_BASE_API + "assets/media/",
      materialdDataList: [],
    };
  },
  computed: {},
  created() {
    this.getList();
    this.currentPageBackgroundSettings =
      this.templatePages[this.currentPage].backgroundSettings;
  },
  methods: {
    getList() {
      this.isLoading = true;
      getList({
        page: this.pageNum,
        pageSize: this.pageSize,
        name: this.form.name,
        created_at_start:
          this.form.date.length > 0 ? this.form.date[0] / 1000 : "",
        created_at_end:
          this.form.date.length > 0 ? this.form.date[1] / 1000 : "",
      }).then((res) => {
        if (res.code === 0) {
          this.dataList = res.data.data;
          this.total = res.data.total;
          this.isLoading = false;
        }
      });
    },


     // 新增：打开工作模板弹窗
  addwork() {
    //  this.$refs.workTemplateFormRef.resetFields();
    this.isShowWorkTemplate = true;
    // 重置表单
    // this.workTemplateForm.name = "";
   
  },

  // 新增：保存工作模板
  handleSaveWorkTemplate() {
    this.$refs.workTemplateFormRef.validate((valid) => {
      if (valid) {
        // 这里替换为实际的 API 调用（例如：addWorkTemplate 接口）
        // 示例逻辑：模拟成功后关闭弹窗并提示
        console.log(this.$refs)
        this.$message.success("工作模板创建成功");
        this.isShowWorkTemplate = false;
        // 可选：刷新模板列表
        this.getList();
      }
    });
  },

  // 新增：关闭弹窗时的重置
  handleWorkTemplateClose() {
      this.isShowWorkTemplate = false;
    this.workTemplateForm.name = "";
  },

    getMaterialList(type) {
      getMaterialList({
        page: this.pageNumM,
        pageSize: this.pageSizeM,
        name: this.form.name,
        type: type,
      }).then((res) => {
        if (res.code === 0) {
          this.materialdDataList = res.data.data;
          this.totalM = res.data.total;
        }
      });
    },
    clearBackground() {
      this.templatePages[this.currentPage].backgroundUrl = "";
      this.templatePages[this.currentPage].backgroundList = [];
    },

    // 在 getDetail 方法中更新 currentPageBackgroundSettings
    async getDetail(id, type) {
      if (type === 1) {
        return new Promise((resolve, reject) => {
          getDetail({
            id,
          }).then((res) => {
            if (res.code === 0) {
              resolve(res.data.template_sm);
            }
          });
        });
      } else {
        getDetail({
          id,
          type: 2,
        }).then((res) => {
          if (res.code === 0) {
            this.sharedAddForm.name = res.data.name;
            this.sharedAddForm.resolution_ratio = res.data.resolution_ratio;

            // 清空现有页面
            this.templatePages = [];

            res.data.template_sm.forEach((item) => {
              const pageIndex = item.template_page - 1;

              // 确保页面存在并初始化所有属性
              if (!this.templatePages[pageIndex]) {
                this.templatePages[pageIndex] = {
                  backgroundUrl: "",
                  materialList: [],
                  backgroundList: [],
                  backgroundSettings: {
                    backgroundSize: "cover", // 默认值
                    backgroundPosition: "center center",
                  },
                };
              }

              // 处理素材
              if (item.template_sm_type === 3) {
                // 恢复背景图和背景设置
                this.templatePages[pageIndex].backgroundList.push(item);
                this.templatePages[pageIndex].backgroundUrl = item.path;

                // 如果后端有保存背景显示设置，则恢复
                if (item.background_display) {
                  this.templatePages[
                    pageIndex
                  ].backgroundSettings.backgroundSize = item.background_display;
                }
              } else {
                this.templatePages[pageIndex].materialList.push(item);
              }
            });

            // 更新 currentPageBackgroundSettings
            this.currentPageBackgroundSettings =
              this.templatePages[this.currentPage].backgroundSettings;
          }
        });
      }
    },

    // 新建模板
    add() {
      this.close();
      this.isShowTemplate = true;
    },
    edit(row) {
      this.id = row.id;
      this.getDetail(row.id);
      this.isEdit = true;
      this.isShowTemplate = true;
    },
    del(id) {
      del(id).then((res) => {
        this.$message({
          type: "success",
          message: this.$i18n.t("public.deleteSuccess"),
        });
        this.getList();
      });
    },
    close() {
      this.templatePages = [
        {
          backgroundUrl: "",
          materialList: [],
          backgroundList: [],
          backgroundSettings: {
            backgroundSize: "cover",
            backgroundPosition: "center center",
          },
        },
      ];
      this.currentPage = 0;
      this.sharedAddForm.name = "";
      this.sharedAddForm.resolution_ratio = "1920x1080";
      this.isEdit = false;
    },
    close1() {},
    // 清空重置模板
    clearTemplate() {
      this.templatePages[this.currentPage].backgroundList = [];
      this.templatePages[this.currentPage].materialList = [];
    },
    // 搜索
    searchForm() {
      this.getList();
    },
    // 重置
    resetForm() {
      this.pageNum = 1;
      this.$refs.form.resetFields();
      this.getList();
    },
    handleSizeChange(val) {
      this.pageNum = 1;
      this.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
    // 单选框选中的数据
    selectChange(selection) {
      if (selection.length > 1) {
        const del_row = selection.shift();
        this.$refs.singleTable.toggleRowSelection(del_row, false);
      }
      this.selectedRow = selection[0];
    },
    // 删除素材
    delMaterial(index) {
      this.templatePages[this.currentPage].materialList.splice(index, 1);
    },
    // 修改 addMaterial 方法
    addMaterial(type) {
      this.type = type;
      if (type === 4) {
        // 直接打开素材选择对话框
        this.getMaterialList(1);
        this.isShowMaterial = true;
      } else if (type === 3) {
        return this.confirmMaterial();
      } else {
        this.getMaterialList(type);
        this.isShowMaterial = true;
      }
    },
    //获取弹窗
    selectFile(){
       this.getMaterialList(3);
        this.isShowMaterial = true;
        console.log(this.isShowMaterial)
    },

    // 修改 confirmMaterial 方法
    confirmMaterial() {
      if (!this.selectedRow) {
        this.$message.warning("请先选择素材");
        return;
      }

      if (this.type === 4) {
        this.templatePages[this.currentPage].backgroundUrl =
          this.selectedRow.path;
        this.templatePages[this.currentPage].backgroundList = [
          {
            type: this.selectedRow.type,
            template_sm_type: 3,
            path: this.selectedRow.path,
            sm_id: this.selectedRow.id,
            sm_name: this.selectedRow.name,
            x_axis: 0,
            y_axis: 0,
            width: 0,
            height: 0,
            template_page: this.currentPage + 1,
            background_display:
              this.currentPageBackgroundSettings.backgroundSize, // 添加当前背景设置
          },
        ];

        // 更新背景设置
        this.currentPageBackgroundSettings =
          this.templatePages[this.currentPage].backgroundSettings;
      } else {
        this.templatePages[this.currentPage].materialList.push({
          type: this.selectedRow.type,
          template_sm_type: this.type === 3 ? 2 : 1,
          path: this.selectedRow.path,
          sm_id: this.selectedRow.id,
          sm_name: this.selectedRow.name,
          x_axis: 0,
          y_axis: 0,
          width: 200,
          height: this.type === 3 ? 50 : 200,
          template_page: this.currentPage + 1,
        });
      }

      this.isShowMaterial = false;
    },

    // 在 el-select 的 change 事件中更新 currentPageBackgroundSettings
    handleBackgroundSizeChange(newValue) {
      this.templatePages[this.currentPage].backgroundSettings.backgroundSize =
        newValue;
      this.currentPageBackgroundSettings =
        this.templatePages[this.currentPage].backgroundSettings;
    },
    confirmMaterial() {
      if (this.type === 4) {
        this.templatePages[this.currentPage].backgroundUrl =
          this.selectedRow.path;
        this.templatePages[this.currentPage].backgroundList = [
          {
            type: this.selectedRow?.type,
            template_sm_type: 3,
            path: this.selectedRow?.path,
            sm_id: this.selectedRow?.id,
            sm_name: this.selectedRow?.name,
            x_axis: 0,
            y_axis: 0,
            width: 0,
            height: 0,
            template_page: this.currentPage + 1,
          },
        ];
      } else {
        this.templatePages[this.currentPage].materialList.push({
          type: this.selectedRow?.type,
          template_sm_type: this.type === 3 ? 2 : 1,
          path: this.selectedRow?.path,
          sm_id: this.selectedRow?.id,
          sm_name: this.selectedRow?.name,
          x_axis: 0,
          y_axis: 0,
          width: 200,
          height: this.type === 3 ? 50 : 200,
          template_page: this.currentPage + 1,
        });
      }

      this.isShowMaterial = false;
    },

    save() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          // 准备所有素材数据，包括背景设置
          let allMaterials = [];
          this.templatePages.forEach((page, pageIndex) => {
            // 添加背景设置到背景素材项
            if (page.backgroundList && page.backgroundList.length > 0) {
              page.backgroundList.forEach((bgItem) => {
                // 为背景素材添加显示设置
                bgItem.background_display =
                  page.backgroundSettings.backgroundSize;
                // 记录所属页面
                bgItem.template_page = pageIndex + 1;
                allMaterials.push(bgItem);
              });
            }

            // 添加普通素材
            page.materialList.forEach((item) => {
              item.template_page = pageIndex + 1;
              allMaterials.push(item);
            });
          });

          if (this.isEdit) {
            // 编辑模板
            edit(
              {
                template_sm: allMaterials,
                resolution_ratio: this.sharedAddForm.resolution_ratio,
                name: this.sharedAddForm.name,
              },
              this.id
            ).then((res) => {
              this.$message({
                type: "success",
                message: this.$i18n.t("public.editSuccess"),
              });
              this.$refs.addForm.resetFields();
              this.isShowTemplate = false;
              this.getList();
            });
          } else {
            // 新增模板
            add({
              template_sm: allMaterials,
              resolution_ratio: this.sharedAddForm.resolution_ratio,
              name: this.sharedAddForm.name,
              type:1,
            }).then((res) => {
              this.$message({
                type: "success",
                message: this.$i18n.t("public.addSuccess"),
              });
              this.$refs.addForm.resetFields();
              this.isShowTemplate = false;
              this.getList();
            });
          }
        }
      });
    },
    // 打包
    confirmPack() {
      this.$refs.packForm.validate((valid) => {
        if (valid) {
          savePack({
            html_content: this.html_content,
            resource_pack_name: this.packForm.resource_pack_name,
            name: this.packForm.name,
            id: this.templateId,
          }).then((res) => {
            this.$message({
              type: "success",
              message: this.$i18n.t("template.form.successTips"),
            });
            this.$refs.packForm.resetFields();
            this.isShowPack = false;
            this.getList();
          });
        }
      });
    },
    // 生成打包 html 资源代码

    savePack(id) {
      this.templateId = id;
      let swiperSlides = "";
      let timeHtml = "";

      // 遍历每一页
      this.templatePages.forEach((page) => {
        let imgHtml = "";
        let backgroundUrl = "";
         // 获取当前页面的背景图显示设置
    const backgroundSize = page.backgroundSettings?.backgroundSize || "cover";
    const backgroundRepeat = backgroundSize === 'repeat' ? 'repeat' : 'no-repeat';

        page.materialList.forEach((item) => {
          if (item.type === 1 && item.template_sm_type === 1) {
            imgHtml += `<img class="img" src="./assets/${item.path}" style="width: ${item.width}px;height: ${item.height}px;top: ${item.y_axis}px;left: ${item.x_axis}px;" alt="">`;
          } else if (item.type === 2 && item.template_sm_type === 1) {
            imgHtml += `<video class="video" src="./assets/${item.path}" style="width: ${item.width}px;height: ${item.height}px;top: ${item.y_axis}px;left: ${item.x_axis}px;" autoplay muted playsinline></video>`;
          } else if (item.template_sm_type === 2) {
            // imgHtml += `<div id="datetime" style="width: ${item.width}px;height: ${item.height}px;top: ${item.y_axis}px;left: ${item.x_axis}px;"></div>`;
             // 修改此处的datetime样式（白色背景+黑色字体）
        imgHtml += `<div id="datetime"
          style="
            width: ${item.width}px;
            height: ${item.height}px;
            top: ${item.y_axis}px;
            left: ${item.x_axis}px;
            color: #000000; /* 黑色字体 */
            background-color: #ffffff; /* 白色背景 */
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          "
        ></div>`;
          }
          if (item.template_sm_type === 2) {
            timeHtml = "updateDateTime();setInterval(updateDateTime, 1000);";
          }
        });

        if (page.backgroundUrl) {
          backgroundUrl = page.backgroundUrl;
        }

        swiperSlides += `
      <div class="swiper-slide" style="
        background-image: url(${backgroundUrl ? "./assets/" + backgroundUrl : ""});
        background-size: ${backgroundSize === 'repeat' ? 'auto' : backgroundSize};
        background-repeat: ${backgroundRepeat};
        background-position: center center;
      ">
        ${imgHtml}
      </div>
    `;
      });

      this.html_content = `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Vue Draggable Resizable Example</title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css">
        <style>
            #main {
                width: 100vw;
                height: 100vh;
                position: relative;
            }
           .img {
                position: absolute;
            }
           .video {
                position: absolute;
                background-color: #000;
            }
            #datetime {
                position: absolute;
                color: #ffffff;
            }
           .swiper {
                width: 100%;
                height: 100%;
            }
           .swiper-slide {
                text-align: center;
                font-size: 18px;
                background: #fff;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        </style>
      </head>
       <body>
        <div class="swiper">
            <div class="swiper-wrapper">
                ${swiperSlides}
            </div>
            <div class="swiper-pagination"></div>
        </div>
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"><\/script>
        <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"><\/script>




      <script>
        function updateDateTime() {
                const now = new Date();
                const optionsDate = { year: 'numeric', month: 'long', day: 'numeric' };
                const optionsTime = { hour: '2 - digit', minute: '2 - digit', second: '2 - digit' };
                let currentDate = now.toLocaleDateString(undefined, optionsDate);
                let currentTime = now.toLocaleTimeString(undefined, optionsTime);
                const datetimeElements = document.querySelectorAll('#datetime');
                datetimeElements.forEach((element) => {
                    element.textContent = currentDate + currentTime;
                });
            }
        ${timeHtml}
         $(document).ready(function () {
                const swiper = new Swiper('.swiper', {
                    pagination: {
                        el: '.swiper-pagination',
                    },
                });
            });
      <\/script>



      </body>
      </html>`;
      this.isShowPack = true;
    },

    handleDragging(x, y, index) {
      this.templatePages[this.currentPage].materialList[index].x_axis = x;
      this.templatePages[this.currentPage].materialList[index].y_axis = y;
    },
    handleResizing(x, y, w, h, index) {
      this.templatePages[this.currentPage].materialList[index].x_axis = x;
      this.templatePages[this.currentPage].materialList[index].y_axis = y;
      this.templatePages[this.currentPage].materialList[index].width = w;
      this.templatePages[this.currentPage].materialList[index].height = h;
    },
    addNewPage() {
      this.templatePages.push({
        backgroundUrl: "",
        materialList: [],
        backgroundList: [],
        backgroundSettings: {
          backgroundSize: "cover",
          backgroundPosition: "center center",
        },
      });
      this.currentPage = this.templatePages.length - 1;
       this.currentPageBackgroundSettings = this.templatePages[this.currentPage].backgroundSettings; // 添加这行

    },
    prevPage() {
      if (this.currentPage > 0) {
        this.currentPage--;
        this.currentPageBackgroundSettings = this.templatePages[this.currentPage].backgroundSettings;

      }
    },
    nextPage() {
      if (this.currentPage < this.templatePages.length - 1) {
        this.currentPage++;
        this.currentPageBackgroundSettings = this.templatePages[this.currentPage].backgroundSettings;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.drag-ball {
  cursor: move;
  width: 50px;
  height: 50px;
  background-image: url("~@/assets/login/background.png");
  background-size: contain;
  background-repeat: repeat;
  z-index: 19;
}

.flex {
  display: flex;
}

.img {
  width: 60px;
  height: 60px;
}

::v-deep .el-dialog {
  margin-top: 0 !important;
}

::v-deep .el-dialog__body {
  padding: 0 10px;
}

.container {
  display: flex;
  height: 80vh;
  margin-top: 6px;
}

.left-pane {
  width: 5%;
  background: #f0f0f0;
  padding: 10px;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.middle-pane {
  width: 80%;
  height: 100%;
  position: relative;
  background-color: #000000;

  .del {
    width: 24px;
    height: 24px;
    position: absolute;
    right: 4px;
    top: 4px;
    cursor: pointer;
    display: none;
  }
}

.draggable:hover .del {
  display: block;
}
.uniform-width {
  width: 80%; /* 或者指定具体像素值，如 200px */
}

.right-pane {
  width: 20%;
  background: #f0f0f0;
  padding: 10px;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.view-item {
  margin-bottom: 10px;

  div {
    text-align: left;
  }
}

.thumbnail {
  width: 100%;
  height: auto;
  max-height: 100px;
  object-fit: cover;
}
// 新增：定义更大的左外边距样式
.ml-large {
  margin-left: 20px; // 可根据需要调整间距大小
}
</style>
