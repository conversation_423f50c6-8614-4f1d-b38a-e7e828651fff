<template>
    <div class="datetime">
      <p>{{ currentDate }} {{ currentTime }}</p>
    </div>
  </template>
  
  <script>
  export default {
    data() {
      return {
        currentDate: '',
        currentTime: ''
      };
    },
    methods: {
      updateDateTime() {
        const now = new Date();
        const optionsDate = { year: 'numeric', month: 'long', day: 'numeric' };
        const optionsTime = { hour: '2-digit', minute: '2-digit', second: '2-digit' };
        this.currentDate = now.toLocaleDateString(undefined, optionsDate);
        this.currentTime = now.toLocaleTimeString(undefined, optionsTime);
      }
    },
    mounted() {
      this.updateDateTime();
      this.intervalId = setInterval(this.updateDateTime, 1000);
    },
    beforeDestroy() {
      clearInterval(this.intervalId);
    }
  };
  </script>
  
  <style scoped>
  .datetime {
    font-family: Arial, sans-serif;
    text-align: center;
    font-size: 1em;
     color: #000000; /* 黑色字体 */
    background-color: #ffffff; /* 纯白色背景 */
    padding: 8px 16px; /* 添加内边距 */
    border-radius: 4px; /* 圆角边框 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 轻微阴影 */
  }
  </style>
  